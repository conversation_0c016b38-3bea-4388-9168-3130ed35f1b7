package models

import (
	"fmt"
	"time"
)

// Severity 漏洞严重程度
type Severity int

const (
	SeverityInfo Severity = iota
	SeverityLow
	SeverityMedium
	SeverityHigh
	SeverityCritical
)

// String 返回严重程度的字符串表示
func (s Severity) String() string {
	switch s {
	case SeverityInfo:
		return "Info"
	case SeverityLow:
		return "Low"
	case SeverityMedium:
		return "Medium"
	case SeverityHigh:
		return "High"
	case SeverityCritical:
		return "Critical"
	default:
		return "Unknown"
	}
}

// Value 返回严重程度的数值
func (s Severity) Value() int {
	return int(s)
}

// Category 漏洞类别
type Category string

const (
	CategoryInjection   Category = "injection"
	CategoryAuth       Category = "authentication"
	CategoryXSS        Category = "xss"
	CategoryCSRF       Category = "csrf"
	CategoryConfig     Category = "misconfiguration"
	CategoryDisclosure Category = "information_disclosure"
	CategoryLogic      Category = "business_logic"
)

// VulnType 具体漏洞类型
type VulnType string

const (
	VulnSQLi             VulnType = "sqli"
	VulnNoSQLi           VulnType = "nosqli"
	VulnCommandInjection VulnType = "command_injection"
	VulnLDAPInjection    VulnType = "ldap_injection"
	VulnXSSReflected     VulnType = "xss_reflected"
	VulnXSSStored        VulnType = "xss_stored"
	VulnXSSDom           VulnType = "xss_dom"
	VulnAuthBypass       VulnType = "auth_bypass"
	VulnIDOR             VulnType = "idor"
	VulnFileUpload       VulnType = "file_upload"
	VulnPathTraversal    VulnType = "path_traversal"
	VulnInfoDisclosure   VulnType = "info_disclosure"
	VulnCORS             VulnType = "cors"
	VulnBackupFiles      VulnType = "backup_files"
)

// Position 参数位置
type Position string

const (
	PositionGET    Position = "GET"
	PositionPOST   Position = "POST"
	PositionHEADER Position = "HEADER"
	PositionCOOKIE Position = "COOKIE"
	PositionJSON   Position = "JSON"
	PositionXML    Position = "XML"
)

// Vulnerability 漏洞信息
type Vulnerability struct {
	ID          string            `json:"id"`
	Type        VulnType          `json:"type"`
	Category    Category          `json:"category"`
	Severity    Severity          `json:"severity"`
	Title       string            `json:"title"`
	Description string            `json:"description"`
	URL         string            `json:"url"`
	Method      string            `json:"method"`
	Parameter   string            `json:"parameter"`
	Position    Position          `json:"position"`
	Payload     string            `json:"payload"`
	Evidence    string            `json:"evidence"`
	Risk        string            `json:"risk"`
	Solution    string            `json:"solution"`
	References  []string          `json:"references,omitempty"`
	CWE         string            `json:"cwe,omitempty"`
	CVSS        float64           `json:"cvss,omitempty"`
	Confidence  float64           `json:"confidence"`
	Verified    bool              `json:"verified"`
	Plugin      string            `json:"plugin"`
	Timestamp   time.Time         `json:"timestamp"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// NewVulnerability 创建新的漏洞实例
func NewVulnerability() *Vulnerability {
	return &Vulnerability{
		Timestamp:  time.Now(),
		Confidence: 0.5,
		Metadata:   make(map[string]string),
	}
}

// VulnerabilityBuilder 漏洞构建器，使用Builder模式
type VulnerabilityBuilder struct {
	vuln *Vulnerability
}

// NewVulnerabilityBuilder 创建漏洞构建器
func NewVulnerabilityBuilder() *VulnerabilityBuilder {
	return &VulnerabilityBuilder{
		vuln: NewVulnerability(),
	}
}

// WithID 设置ID
func (b *VulnerabilityBuilder) WithID(id string) *VulnerabilityBuilder {
	b.vuln.ID = id
	return b
}

// WithType 设置漏洞类型
func (b *VulnerabilityBuilder) WithType(vulnType VulnType) *VulnerabilityBuilder {
	b.vuln.Type = vulnType
	return b
}

// WithCategory 设置类别
func (b *VulnerabilityBuilder) WithCategory(category Category) *VulnerabilityBuilder {
	b.vuln.Category = category
	return b
}

// WithSeverity 设置严重程度
func (b *VulnerabilityBuilder) WithSeverity(severity Severity) *VulnerabilityBuilder {
	b.vuln.Severity = severity
	return b
}

// WithTitle 设置标题
func (b *VulnerabilityBuilder) WithTitle(title string) *VulnerabilityBuilder {
	b.vuln.Title = title
	return b
}

// WithDescription 设置描述
func (b *VulnerabilityBuilder) WithDescription(description string) *VulnerabilityBuilder {
	b.vuln.Description = description
	return b
}

// WithURL 设置URL
func (b *VulnerabilityBuilder) WithURL(url string) *VulnerabilityBuilder {
	b.vuln.URL = url
	return b
}

// WithMethod 设置HTTP方法
func (b *VulnerabilityBuilder) WithMethod(method string) *VulnerabilityBuilder {
	b.vuln.Method = method
	return b
}

// WithParameter 设置参数信息
func (b *VulnerabilityBuilder) WithParameter(name string, position Position) *VulnerabilityBuilder {
	b.vuln.Parameter = name
	b.vuln.Position = position
	return b
}

// WithPayload 设置载荷
func (b *VulnerabilityBuilder) WithPayload(payload string) *VulnerabilityBuilder {
	b.vuln.Payload = payload
	return b
}

// WithEvidence 设置证据
func (b *VulnerabilityBuilder) WithEvidence(evidence string) *VulnerabilityBuilder {
	b.vuln.Evidence = evidence
	return b
}

// WithRisk 设置风险说明
func (b *VulnerabilityBuilder) WithRisk(risk string) *VulnerabilityBuilder {
	b.vuln.Risk = risk
	return b
}

// WithSolution 设置修复方案
func (b *VulnerabilityBuilder) WithSolution(solution string) *VulnerabilityBuilder {
	b.vuln.Solution = solution
	return b
}

// WithReferences 设置参考链接
func (b *VulnerabilityBuilder) WithReferences(references []string) *VulnerabilityBuilder {
	b.vuln.References = references
	return b
}

// WithCWE 设置CWE编号
func (b *VulnerabilityBuilder) WithCWE(cwe string) *VulnerabilityBuilder {
	b.vuln.CWE = cwe
	return b
}

// WithCVSS 设置CVSS评分
func (b *VulnerabilityBuilder) WithCVSS(cvss float64) *VulnerabilityBuilder {
	b.vuln.CVSS = cvss
	return b
}

// WithConfidence 设置置信度
func (b *VulnerabilityBuilder) WithConfidence(confidence float64) *VulnerabilityBuilder {
	b.vuln.Confidence = confidence
	return b
}

// WithVerified 设置验证状态
func (b *VulnerabilityBuilder) WithVerified(verified bool) *VulnerabilityBuilder {
	b.vuln.Verified = verified
	return b
}

// WithPlugin 设置插件名称
func (b *VulnerabilityBuilder) WithPlugin(plugin string) *VulnerabilityBuilder {
	b.vuln.Plugin = plugin
	return b
}

// WithMetadata 添加元数据
func (b *VulnerabilityBuilder) WithMetadata(key, value string) *VulnerabilityBuilder {
	b.vuln.Metadata[key] = value
	return b
}

// Build 构建漏洞实例
func (b *VulnerabilityBuilder) Build() *Vulnerability {
	// 自动分类
	if b.vuln.Category == "" {
		b.vuln.Category = b.deriveCategory(b.vuln.Type)
	}
	
	// 生成ID（如果没有设置）
	if b.vuln.ID == "" {
		b.vuln.ID = generateVulnID(b.vuln)
	}
	
	return b.vuln
}

// deriveCategory 根据漏洞类型推导类别
func (b *VulnerabilityBuilder) deriveCategory(vulnType VulnType) Category {
	switch vulnType {
	case VulnSQLi, VulnNoSQLi, VulnCommandInjection, VulnLDAPInjection:
		return CategoryInjection
	case VulnXSSReflected, VulnXSSStored, VulnXSSDom:
		return CategoryXSS
	case VulnAuthBypass, VulnIDOR:
		return CategoryAuth
	case VulnInfoDisclosure, VulnBackupFiles:
		return CategoryDisclosure
	case VulnCORS:
		return CategoryConfig
	default:
		return CategoryLogic
	}
}

// generateVulnID 生成漏洞ID
func generateVulnID(vuln *Vulnerability) string {
	// 简单的ID生成策略，实际项目中可以使用UUID或其他策略
	return fmt.Sprintf("%s_%s_%d", 
		string(vuln.Type),
		vuln.Parameter,
		vuln.Timestamp.Unix(),
	)
}