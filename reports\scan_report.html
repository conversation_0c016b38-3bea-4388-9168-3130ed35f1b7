
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DroneRiskScan 安全扫描报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            color: #333; 
            line-height: 1.6; 
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .report-header { 
            background: white; 
            border-radius: 20px; 
            padding: 40px; 
            margin-bottom: 30px; 
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); 
        }
        .report-header h1 { color: #333; font-size: 2.5em; margin-bottom: 10px; }
        .report-header .meta { color: #666; }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .stat-card { 
            background: white; 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
            text-align: center; 
            transition: all 0.3s; 
        }
        .stat-card:hover { 
            transform: translateY(-10px); 
            box-shadow: 0 20px 40px rgba(0,0,0,0.15); 
        }
        .stat-card .number { 
            font-size: 3em; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        .stat-card.critical .number { color: #dc3545; }
        .stat-card.high .number { color: #fd7e14; }
        .stat-card.medium .number { color: #ffc107; }
        .stat-card.low .number { color: #28a745; }
        .stat-card.info .number { color: #17a2b8; }
        .stat-card.total .number { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
        }
        .stat-card .label { 
            color: #888; 
            font-size: 1.1em; 
            text-transform: uppercase; 
            letter-spacing: 1px; 
        }
        .vulns-section { 
            background: white; 
            border-radius: 20px; 
            padding: 40px; 
            box-shadow: 0 20px 60px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
        }
        .vulns-section h2 { color: #333; margin-bottom: 30px; font-size: 2em; }
        .vuln-item { 
            border-left: 5px solid #ddd; 
            padding: 20px; 
            margin-bottom: 20px; 
            background: #f8f9fa; 
            border-radius: 10px; 
            transition: all 0.3s; 
        }
        .vuln-item:hover { 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1); 
            transform: translateX(5px); 
        }
        .vuln-item.severity-4 { 
            border-left-color: #dc3545; 
            background: linear-gradient(to right, #fff5f5, white); 
        }
        .vuln-item.severity-3 { 
            border-left-color: #fd7e14; 
            background: linear-gradient(to right, #fff9f5, white); 
        }
        .vuln-item.severity-2 { 
            border-left-color: #ffc107; 
            background: linear-gradient(to right, #fffef5, white); 
        }
        .vuln-item.severity-1 { 
            border-left-color: #28a745; 
            background: linear-gradient(to right, #f5fff5, white); 
        }
        .vuln-item.severity-0 { 
            border-left-color: #17a2b8; 
            background: linear-gradient(to right, #f5fffe, white); 
        }
        .vuln-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 15px; 
        }
        .vuln-title { font-size: 1.3em; font-weight: bold; color: #333; }
        .severity-badge { 
            padding: 5px 15px; 
            border-radius: 20px; 
            font-size: 0.9em; 
            font-weight: bold; 
            text-transform: uppercase; 
        }
        .severity-4 { background: #dc3545; color: white; }
        .severity-3 { background: #fd7e14; color: white; }
        .severity-2 { background: #ffc107; color: black; }
        .severity-1 { background: #28a745; color: white; }
        .severity-0 { background: #17a2b8; color: white; }
        .vuln-details { color: #666; line-height: 1.8; }
        .vuln-details strong { color: #333; }
        .no-vulns { 
            text-align: center; 
            padding: 60px; 
            color: #28a745; 
            font-size: 1.5em; 
        }
        .footer { 
            text-align: center; 
            color: white; 
            margin-top: 40px; 
            padding: 20px; 
        }
        .footer a { color: white; text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-header">
            <h1>🚁 DroneRiskScan Security Report</h1>
            <div class="meta">
                <p>📅 Generated: 2025-08-07 09:31:29</p>
                <p>🎯 Targets: 1</p>
                <p>📊 Total Vulnerabilities: 0</p>
                <p>⏱️ Scan Duration: 144.411714ms</p>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="number">0</div>
                <div class="label">Total</div>
            </div>
            <div class="stat-card critical">
                <div class="number">0</div>
                <div class="label">Critical</div>
            </div>
            <div class="stat-card high">
                <div class="number">0</div>
                <div class="label">High</div>
            </div>
            <div class="stat-card medium">
                <div class="number">0</div>
                <div class="label">Medium</div>
            </div>
            <div class="stat-card low">
                <div class="number">0</div>
                <div class="label">Low</div>
            </div>
            <div class="stat-card info">
                <div class="number">0</div>
                <div class="label">Info</div>
            </div>
        </div>
        
        <div class="vulns-section">
            <h2>🔍 Vulnerability Details</h2>
            
                <div class="no-vulns">✅ No vulnerabilities found!</div>
            
        </div>
        
        <div class="footer">
            <p>Generated by DroneRiskScan v1.0 | Professional Drone Security Scanner</p>
        </div>
    </div>
</body>
</html>